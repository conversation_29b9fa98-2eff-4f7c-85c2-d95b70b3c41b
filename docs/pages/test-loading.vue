<template>
  <div class="test-loading-page">
    <h1>Loading测试页面</h1>
    
    <div class="test-section">
      <h2>测试按钮</h2>
      <el-button @click="simulateSlowLoading" type="primary">模拟慢速加载</el-button>
      <el-button @click="simulateImageLoading" type="success">模拟图片加载</el-button>
      <el-button @click="forceFinishLoading" type="warning">强制完成Loading</el-button>
      <el-button @click="resetLoading" type="danger">重置Loading</el-button>
    </div>

    <div class="test-section">
      <h2>动态内容区域</h2>
      <div id="dynamic-content">
        <!-- 动态内容将在这里插入 -->
      </div>
    </div>

    <div class="test-section">
      <h2>图片加载测试</h2>
      <div class="image-container" v-if="showImages">
        <img 
          v-for="(url, index) in imageUrls" 
          :key="index"
          :src="url" 
          :alt="`Test image ${index + 1}`"
          class="test-image"
          @load="onImageLoad"
          @error="onImageError"
        />
      </div>
    </div>

    <div class="test-section">
      <h2>Loading状态信息</h2>
      <div class="status-info">
        <p>页面加载状态: {{ document?.readyState || 'unknown' }}</p>
        <p>图片加载完成: {{ loadedImages }} / {{ totalImages }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 导入必要的组合式函数
import { ref, watch } from 'vue';

const { start, finish, reset } = useTopLoading();

const showImages = ref(false);
const loadedImages = ref(0);
const totalImages = ref(0);

// 测试图片URL
const imageUrls = [
  'https://picsum.photos/300/200?random=1',
  'https://picsum.photos/300/200?random=2',
  'https://picsum.photos/300/200?random=3',
  'https://picsum.photos/300/200?random=4',
  'https://picsum.photos/300/200?random=5'
];

// 模拟慢速加载
const simulateSlowLoading = () => {
  start();

  // 模拟一些异步操作
  setTimeout(() => {
    const dynamicContent = document.getElementById('dynamic-content');
    if (dynamicContent) {
      dynamicContent.innerHTML = `
        <p>动态加载的内容 - ${new Date().toLocaleTimeString()}</p>
        <div class="loading-test">
          <p>这是一些模拟的慢速加载内容</p>
          <p>加载时间: ${Math.random() * 3 + 1}秒</p>
        </div>
      `;
    }

    // 模拟资源加载完成
    setTimeout(() => {
      finish();
    }, 2000);
  }, 1000);
};

// 模拟图片加载
const simulateImageLoading = () => {
  start();
  loadedImages.value = 0;
  totalImages.value = imageUrls.length;
  showImages.value = true;

  // 如果所有图片都加载完成，自动完成loading
  watch(loadedImages, (newVal) => {
    if (newVal >= totalImages.value) {
      setTimeout(() => {
        finish();
      }, 500);
    }
  });
};

// 图片加载完成
const onImageLoad = () => {
  loadedImages.value++;
};

// 图片加载错误
const onImageError = () => {
  loadedImages.value++;
};

// 强制完成Loading
const forceFinishLoading = () => {
  finish();
};

// 重置Loading
const resetLoading = () => {
  reset();
  showImages.value = false;
  loadedImages.value = 0;
  totalImages.value = 0;

  const dynamicContent = document.getElementById('dynamic-content');
  if (dynamicContent) {
    dynamicContent.innerHTML = '';
  }
};

// 页面标题和元数据
useHead({
  title: 'Loading测试页面'
});

definePageMeta({
  title: 'Loading测试页面',
  description: '用于测试顶部Loading条功能的页面'
});
</script>

<style scoped>
.test-loading-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #f9f9f9;
}

.test-section h2 {
  margin-top: 0;
  color: #333;
}

.el-button {
  margin-right: 10px;
  margin-bottom: 10px;
}

.image-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.test-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.status-info {
  background: #fff;
  padding: 15px;
  border-radius: 6px;
  border-left: 4px solid #e25a1b;
}

.status-info p {
  margin: 5px 0;
  font-family: monospace;
}

.loading-test {
  background: #fff;
  padding: 15px;
  border-radius: 6px;
  margin-top: 10px;
}

#dynamic-content {
  min-height: 50px;
  background: #fff;
  padding: 15px;
  border-radius: 6px;
}
</style>
