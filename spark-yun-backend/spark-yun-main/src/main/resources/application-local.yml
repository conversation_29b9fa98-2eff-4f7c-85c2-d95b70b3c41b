# 服务端口号
server:
  port: 8080

spring:

  # 配置后台服务访问管理员账号密码
  # druid: http://localhost:8080/druid/index.html
  # h2: http://localhost:8080/h2-console
  # swagger: http://localhost:8080/swagger-ui/index.html
  security:
    user:
      name: admin
      password: admin123

  # 业务数据库的信息配置，默认使用h2数据库，使用全新的db
  # h2驱动: org.h2.Driver
  # mysql驱动: com.mysql.cj.jdbc.Driver
  # postgres驱动: org.postgresql.Driver
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:file:~/.zhiqingyun/h2/data;AUTO_SERVER=TRUE
    username: root
    password: root123

jasypt:
  encryptor:
    password: zhiqingyun

# 至轻云应用配置
isx-app:
  resources-path: classpath:resources # 资源文件，例如许可证、驱动、头像等资源路径
  admin-passwd: admin123 # 配置至轻云平台admin管理员的密码，仅第一次执行生效
  use-ssl: false # 是否开启ssl