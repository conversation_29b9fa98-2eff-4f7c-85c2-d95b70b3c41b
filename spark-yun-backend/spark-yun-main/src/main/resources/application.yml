application:
  title: 至轻云
  website: https://zhiqingyun.isxcode.com
  describe: 超轻量级智能化大数据中心

server:
  tomcat:
    connection-timeout: 120s

spring:

  profiles:
    active: local

  security:
    user:
      roles: ADMIN

  main:
    banner-mode: log

  web:
    resources:
      chain:
        cache: true
        compressed: true
      cache:
        period: 31536000 # 单位秒，默认缓存365天

  servlet:
    multipart:
      max-file-size: 5GB
      max-request-size: 5GB
      enabled: true

  banner:
    location: classpath:logo.txt

  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initial-size: 10
      max-active: 200
      min-idle: 10
      max-wait: 30000
      test-while-idle: true
      filters: stat,wall,log4j2
      validationQuery: select 'x'
      stat-view-servlet:
        enabled: true
        allow:

  jpa:
    show-sql: false
    open-in-view: false
    hibernate:
      ddl-auto: none
      naming:
        physical-strategy: com.isxcode.spark.config.JpaTableUpperCaseStrategy
    properties:
      hibernate:
        session_factory:
          statement_inspector: com.isxcode.spark.config.JpaTenantInterceptor

  mvc:
    view:
      prefix: /templates/
      suffix: .html
    static-path-pattern: /static/**

  h2:
    console:
      enabled: true
      path: /h2-console
      settings:
        trace: false
        web-allow-others: true

  quartz:
    scheduler-name: spark-yun-quartz-scheduler
    job-store-type: jdbc
    jdbc:
      initialize-schema: never

  cache:
    type: simple

  thymeleaf:
    check-template-location: false

  api-docs:
    path: /swagger-ui/api-docs
  swagger-ui:
    path: /swagger-ui.html
  packages-to-scan: com.isxcode.spark

  flyway:
    enabled: true

logging:
  level:
    root: info
    com.isxcode.spark: info
  exception-conversion-word: '%wEx'
  file:
    name: ./logs/spark-yun.log
  pattern:
    level: '%5p'
    dateformat: 'yyyy-MM-dd HH:mm:ss.SSS'
    console: '%clr(%d{${LOG_DATEFORMAT_PATTERN}}){faint} %clr(${LOG_LEVEL_PATTERN}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD}'
    file: '%d{${LOG_DATEFORMAT_PATTERN}} ${LOG_LEVEL_PATTERN} %-5(${PID:- }) --- [%15.15t] %-40.40logger{39} : %m%n${LOG_EXCEPTION_CONVERSION_WORD}}'
  register-shutdown-hook: false
  logback:
    rollingpolicy:
      clean-history-on-start: false
      file-name-pattern: '${LOG_FILE}.%d{yyyy-MM-dd}.%i.gz'
      total-size-cap: 100MB
      max-history: 30
      max-file-size: 10MB

jasypt:
  encryptor:
    property:
      suffix: )
      prefix: ENC(

isx-app:
  admin-url:
    - /swagger-ui/** # http://localhost:8080/swagger-ui/index.html
    - /v3/** # swagger地址
    - /h2-console/** # http://localhost:8080/h2-console
    - /druid/** # http://localhost:8080/druid/index.html
    - /**/developer/** # 开发者接口
  anonymous-url:
    - / # 让页面可以访问前端
    - /static/** # 放行前端资源文件
    - /favicon.ico # 放行网站的logo
    - /login # 放行登录接口
    - /ssoauth # 单点登录
    - /auth # 放行登录接口
    - /share/** # 分享表单
    - /dashboard/** # 分享大屏
    - /**/open/** # 放行所有的开放接口
    - /vip/**/open/** # 放行所有的开放接口
    - /**/api/** # 放行所有自定义接口
    - /**/api/**/** # 放行所有自定义接口
    - /**/api/**/**/** # 放行所有自定义接口
    - /home/<USER>
  anonymous-role-url:
    - /vip/form/getFormConfigForAnonymous # 匿名者获取表单配置